const { expect } = require("chai");
const { ethers, providers } = require("hardhat");

describe("setFeeSeller", () => {
  it("should 1%", async () => {
    let PaydeceEscrow, paydeceEscrow;
    const [owner, Seller, Buyer, other] = await ethers.getSigners();

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    await paydeceEscrow.setFeeSeller(1);

    const feeBuyer = await paydeceEscrow.feeSeller();

    expect(Number(feeBuyer)).to.equal(Number(1));

    await expect(
      paydeceEscrow.connect(other).setFeeSeller("1000")
    ).to.be.revertedWith("caller is not the owner");
  });

  it("should revert with message 'The fee can be from 0% to 1%", async () => {
    let PaydeceEscrow, paydeceEscrow;

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    await expect(paydeceEscrow.setFeeSeller(1001)).to.be.revertedWith(
      "The fee can be from 0% to 1%"
    );
  });
});

describe("setFeeBuyer", () => {
  it("should revert with message 'The fee can be from 0% to 1%", async () => {
    let PaydeceEscrow, paydeceEscrow;
    const [owner, Seller, Buyer, other] = await ethers.getSigners();

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    await expect(paydeceEscrow.setFeeBuyer(1001)).to.be.revertedWith(
      "The fee can be from 0% to 1%"
    );

    await expect(
      paydeceEscrow.connect(other).setFeeBuyer(1)
    ).to.be.revertedWith("Ownable: caller is not the owner");

    await paydeceEscrow.setFeeBuyer(1);
    const feeBuyer = await paydeceEscrow.feeBuyer();
    expect(Number(feeBuyer)).to.equal(Number(1));
  });
});

describe("addStablesAddresses & delStablesAddresses", () => {
  it("you should add and remove the EC20 stable addresses", async () => {
    let PaydeceEscrow, paydeceEscrow;
    const [owner, Seller, Buyer, other] = await ethers.getSigners();

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    const usdtAddress = "******************************************";
    await paydeceEscrow.addStablesAddresses(usdtAddress);
    await paydeceEscrow.delStablesAddresses(usdtAddress);

    const usdcAddress = "******************************************";
    await paydeceEscrow.addStablesAddresses(usdcAddress);
    await paydeceEscrow.delStablesAddresses(usdcAddress);

    //delStablesAddresses Error
    await expect(
      paydeceEscrow.connect(other).delStablesAddresses(usdcAddress)
    ).to.be.revertedWith("Ownable: caller is not the owner");
  });
});

describe("PaydeceEscrow StableCoin", function () {
  //StableCoin
  it("should create a escrow and release StableCoin", async function () {
    let PaydeceEscrow, paydeceEscrow;
    let USDT, usdt;

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    // Deploy USDT
    USDT = await ethers.getContractFactory("USDTToken");
    usdt = await USDT.deploy();
    await usdt.deployed();

    const [owner, Seller, Buyer, other] = await ethers.getSigners();
    // console.log(owner.address)

    // let sellerBalance = await ethers.provider.getBalance(Seller.address);
    // console.log("sellerBalance:"+sellerBalance.toString())

    //get balance sc paydece
    // const prevBalanceSC = await ethers.provider.getBalance(paydeceEscrow.address);
    // console.log(prevBalance.toString())

    //Set Fee to 1%
    await paydeceEscrow.connect(owner).setFeeSeller("1000");

    //Set amount
    const decimals = await usdt.connect(Buyer).decimals();
    const ammount = 100 * 10 ** decimals;
    //  console.log("ammount:"+ammount)

    await expect(
      paydeceEscrow
        .connect(Seller)
        .createEscrow("1", Seller.address, ammount, usdt.address)
    ).to.be.revertedWith("Address Stable to be whitelisted");

    //call addStablesAddresses
    await paydeceEscrow.connect(owner).addStablesAddresses(usdt.address);

    //transfer
    await usdt.transfer(Buyer.address, ammount);

    await expect(
      paydeceEscrow
        .connect(Buyer)
        .createEscrow("1", Seller.address, ammount, usdt.address)
    ).to.be.revertedWith("Seller approve to Escrow first");

    //call approve
    await usdt.connect(Buyer).approve(paydeceEscrow.address, ammount);

    await expect(
      paydeceEscrow
        .connect(Seller)
        .createEscrow("1", Seller.address, ammount, usdt.address)
    ).to.be.revertedWith("seller cannot be the same as buyer");

    //call createEscrow
    await paydeceEscrow
      .connect(Buyer)
      .createEscrow("1", Seller.address, ammount, usdt.address);

    await expect(
      paydeceEscrow
        .connect(Buyer)
        .createEscrow("1", Seller.address, ammount, usdt.address)
    ).to.be.revertedWith("Escrow already exists");

    //call releaseEscrow Error
    await expect(
      paydeceEscrow.connect(other).releaseEscrow("1")
    ).to.be.revertedWith("Only Buyer can call this");

    //call releaseEscrow
    await paydeceEscrow.connect(Buyer).releaseEscrow("1");

    //get Balance
    const scBalance = await usdt.balanceOf(paydeceEscrow.address);
    expect(Number(scBalance)).to.equal(Number(1 * 10 ** decimals));
    const buyerBalance = await usdt.balanceOf(Buyer.address);
    expect(Number(buyerBalance)).to.equal(Number(0));
    const sellerBalance = await usdt.balanceOf(Seller.address);
    expect(Number(sellerBalance)).to.equal(Number(99 * 10 ** decimals));
  });

  it("should releaseEscrowOwner to Seller StableCoin", async function () {
    let PaydeceEscrow, paydeceEscrow;
    let USDT, usdt;

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    // Deploy USDT
    USDT = await ethers.getContractFactory("USDTToken");
    usdt = await USDT.deploy();
    await usdt.deployed();

    const [owner, Seller, Buyer, other] = await ethers.getSigners();

    //Set Fee to 1%
    await paydeceEscrow.connect(owner).setFeeSeller("1000");

    //call addStablesAddresses
    await paydeceEscrow.connect(owner).addStablesAddresses(usdt.address);

    //Set amount
    const decimals = await usdt.connect(Buyer).decimals();
    const ammount = 100 * 10 ** decimals;
    //  console.log("ammount:"+ammount)

    //transfer
    await usdt.transfer(Buyer.address, ammount);

    //call approve
    await usdt.connect(Buyer).approve(paydeceEscrow.address, ammount);

    //call createEscrow
    await paydeceEscrow
      .connect(Buyer)
      .createEscrow("1", Seller.address, ammount, usdt.address);

    //call releaseEscrowOwner Error
    await expect(
      paydeceEscrow.connect(other).releaseEscrowOwner("1")
    ).to.be.revertedWith("Ownable: caller is not the owner");

    //call releaseEscrowOwner
    await paydeceEscrow.connect(owner).releaseEscrowOwner("1");

    //get Balance
    const scBalance = await usdt.balanceOf(paydeceEscrow.address);
    //  console.log("scBalance:"+scBalance)
    expect(Number(scBalance)).to.equal(Number(1 * 10 ** decimals));
    const buyerBalance = await usdt.balanceOf(Buyer.address);
    //  console.log("buyerBalance:"+buyerBalance)
    expect(Number(buyerBalance)).to.equal(Number(0));
    const sellerBalance = await usdt.balanceOf(Seller.address);
    //  console.log("sellerBalance:"+sellerBalance)
    expect(Number(sellerBalance)).to.equal(Number(99 * 10 ** decimals));
  });

  it("should refund to Buyer StableCoin", async function () {
    let PaydeceEscrow, paydeceEscrow;
    let USDT, usdt;

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    // Deploy USDT
    USDT = await ethers.getContractFactory("USDTToken");
    usdt = await USDT.deploy();
    await usdt.deployed();

    const [owner, Seller, Buyer, other] = await ethers.getSigners();

    //Set Fee to 1%
    await paydeceEscrow.connect(owner).setFeeSeller("1000");

    //call addStablesAddresses
    await paydeceEscrow.connect(owner).addStablesAddresses(usdt.address);

    //Set amount
    const decimals = await usdt.connect(Buyer).decimals();
    const ammount = 100 * 10 ** decimals;
    //  console.log("ammount:"+ammount)

    //transfer
    await usdt.transfer(Buyer.address, ammount);

    //call approve
    await usdt.connect(Buyer).approve(paydeceEscrow.address, ammount);

    //call createEscrow
    await paydeceEscrow
      .connect(Buyer)
      .createEscrow("1", Seller.address, ammount, usdt.address);

    //call refundBuyer Error
    await expect(
      paydeceEscrow.connect(other).refundBuyer("1")
    ).to.be.revertedWith("Ownable: caller is not the owner");

    //call refundBuyer
    await paydeceEscrow.connect(owner).refundBuyer("1");

    //get Balance
    const scBalance = await usdt.balanceOf(paydeceEscrow.address);
    //  console.log("==========scBalance:"+scBalance)
    expect(Number(scBalance)).to.equal(Number(0));
    const buyerBalance = await usdt.balanceOf(Buyer.address);
    //  console.log("==========buyerBalance:"+buyerBalance)
    expect(Number(buyerBalance)).to.equal(Number(100 * 10 ** decimals));
    const sellerBalance = await usdt.balanceOf(Seller.address);
    //  console.log("==========sellerBalance:"+sellerBalance)
    expect(Number(sellerBalance)).to.equal(Number(0));
  });

  it("should feesAvailable & withdrawFees StableCoin", async function () {
    let PaydeceEscrow, paydeceEscrow;
    let USDT, usdt;

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    // Deploy USDT
    USDT = await ethers.getContractFactory("USDTToken");
    usdt = await USDT.deploy();
    await usdt.deployed();

    const [owner, Seller, Buyer, other] = await ethers.getSigners();

    //Set Fee to 1%
    await paydeceEscrow.connect(owner).setFeeSeller("1000");

    //call addStablesAddresses
    await paydeceEscrow.connect(owner).addStablesAddresses(usdt.address);

    //Set amount
    const decimals = await usdt.connect(Buyer).decimals();
    const ammount = 100 * 10 ** decimals;
    //  console.log("ammount:"+ammount)

    //transfer
    await usdt.transfer(Buyer.address, ammount);

    //call approve
    await usdt.connect(Buyer).approve(paydeceEscrow.address, ammount);

    await expect(
      paydeceEscrow.connect(other).withdrawFees(usdt.address)
    ).to.be.revertedWith("Ownable: caller is not the owner");

    await expect(
      paydeceEscrow.connect(owner).withdrawFees(usdt.address)
    ).to.be.revertedWith("Amount > feesAvailable");

    //call createEscrow
    await paydeceEscrow
      .connect(Buyer)
      .createEscrow("1", Seller.address, ammount, usdt.address);

    //call releaseEscrowOwner
    await paydeceEscrow.connect(owner).releaseEscrowOwner("1");

    const _feesAvailable = await paydeceEscrow
      .connect(owner)
      .feesAvailable(usdt.address);
    // console.log("feesAvailable:" + _feesAvailable);

    const scBalance = await usdt.balanceOf(owner.address);

    // console.log("scBalance:" + scBalance);
    //withdrawFees
    const _releaseEscrowOwner = await paydeceEscrow
      .connect(owner)
      .withdrawFees(usdt.address);

    //get Balance
    const scAfterBalance = await usdt.balanceOf(owner.address);
    // console.log("scAfterBalance:" + scAfterBalance);

    expect(Number(scAfterBalance)).to.equal(
      Number(scBalance) + Number(_feesAvailable)
    );
  });
});

describe("PaydeceEscrow NativeCoin", function () {
  //NativeCoin

  it("should create a escrow and release NativeCoin", async function () {
    let PaydeceEscrow, paydeceEscrow;

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    const [owner, Seller, Buyer, other] = await ethers.getSigners();
    // console.log(owner.address)

    const initBuyerBalance = await ethers.provider.getBalance(Seller.address);
    const initSellerBalance = await ethers.provider.getBalance(Buyer.address);

    //Set Fee to 1%
    await paydeceEscrow.connect(owner).setFeeSeller("1000");

    //call createEscrow
    const ammount = ethers.utils.parseUnits("100", "ether"); //1 ether

    await expect(
      paydeceEscrow
        .connect(Seller)
        .createEscrowNativeCoin("2", Seller.address, ammount, {
          value: ammount,
        })
    ).to.be.revertedWith("seller cannot be the same as buyer");

    //Error the ammount
    await expect(
      paydeceEscrow
        .connect(Buyer)
        .createEscrowNativeCoin("2", Seller.address, ammount, {
          value: 0,
        })
    ).to.be.revertedWith("Incorrect amount");

    await paydeceEscrow
      .connect(Buyer)
      .createEscrowNativeCoin("2", Seller.address, ammount, { value: ammount });

    await expect(
      paydeceEscrow
        .connect(Buyer)
        .createEscrowNativeCoin("2", Seller.address, ammount, {
          value: ammount,
        })
    ).to.be.revertedWith("Escrow already exists");

    //call releaseEscrow Error
    await expect(
      paydeceEscrow.connect(other).releaseEscrowNativeCoin("2")
    ).to.be.revertedWith("Only Buyer can call this");

    //call releaseEscrow
    await paydeceEscrow.connect(Buyer).releaseEscrowNativeCoin("2");

    //get balance SC paydece
    const afterbalanceSC = await ethers.provider.getBalance(
      paydeceEscrow.address
    );
    // console.log("afterbalanceSC:"+afterbalanceSC.toString())
    // 1 is expected because 1% of 100
    expect(Number(afterbalanceSC)).to.equal(
      Number(ethers.utils.parseUnits("1", "ether"))
    );

    buyerBalance = await ethers.provider.getBalance(Buyer.address);
    // console.log("buyerBalance:"+buyerBalance.toString())

    // console.log("ammount:"+ammount)

    sellerBalance = await ethers.provider.getBalance(Seller.address);
    // console.log("sellerBalance:"+sellerBalance.toString())
    // expect(Number(sellerBalance)).to.equal(Number(initSellerBalance)+Number(ethers.utils.parseUnits("99", "ether")));
  });

  it("should releaseEscrowOwnerNativeCoin to Seller NativeCoin", async function () {
    let PaydeceEscrow, paydeceEscrow;

    const [owner, Seller, Buyer, other] = await ethers.getSigners();

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    let buyerBalance = await ethers.provider.getBalance(Buyer.address);
    // console.log("----------antes buyerBalance:"+buyerBalance.toString())

    let sellerBalance = await ethers.provider.getBalance(Seller.address);
    // console.log("sellerBalance:"+sellerBalance.toString())

    //get balance sc paydece
    const prevBalanceSC = await ethers.provider.getBalance(
      paydeceEscrow.address
    );
    // console.log(prevBalance.toString())

    //Set Fee to 1%
    await paydeceEscrow.connect(owner).setFeeSeller("1000");

    //call createEscrow
    const ammount = ethers.utils.parseUnits("100", "ether"); //1 ether
    await paydeceEscrow
      .connect(Buyer)
      .createEscrowNativeCoin("2", Seller.address, ammount, { value: ammount });

    //call releaseEscrow Error
    await expect(
      paydeceEscrow.connect(other).releaseEscrowOwnerNativeCoin("2")
    ).to.be.revertedWith("Ownable: caller is not the owner");

    //call releaseEscrow
    await paydeceEscrow.connect(owner).releaseEscrowOwnerNativeCoin("2");

    //get balance sc paydece
    const afterbalanceSC = await ethers.provider.getBalance(
      paydeceEscrow.address
    );
    //  console.log("----------afterbalanceSC:"+afterbalanceSC.toString())
    // expect(Number(afterbalanceSC)).to.equal(Number(0));

    sellerBalance = await ethers.provider.getBalance(Seller.address);
    // console.log("----------sellerBalance:"+sellerBalance.toString())

    buyerBalance = await ethers.provider.getBalance(Buyer.address);
    //  console.log("----------buyerBalance:"+buyerBalance.toString())

    // 1 is expected because 1% of 100
    // /expect(Number(afterbalanceSC)).to.equal(Number(ethers.utils.parseUnits("1", "ether")));
    // expect(Number(buyerBalance)).to.be.at.least(Number(ethers.utils.parseUnits("100", "ether")));
  });

  it("should refund to Buyer NativeCoin", async function () {
    let PaydeceEscrow, paydeceEscrow;
    let USDT, usdt;

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    // Deploy USDT
    USDT = await ethers.getContractFactory("USDTToken");
    usdt = await USDT.deploy();
    await usdt.deployed();

    const [owner, Seller, Buyer, other] = await ethers.getSigners();
    // console.log(owner.address)

    let sellerBalance = await ethers.provider.getBalance(Seller.address);
    // console.log("sellerBalance:"+sellerBalance.toString())

    //get balance sc paydece
    const prevBalanceSC = await ethers.provider.getBalance(
      paydeceEscrow.address
    );
    // console.log(prevBalance.toString())

    //Set Fee to 1%
    await paydeceEscrow.connect(owner).setFeeSeller("1000");

    //call createEscrow
    const ammount = ethers.utils.parseUnits("100", "ether"); //1 ether
    await paydeceEscrow
      .connect(Buyer)
      .createEscrowNativeCoin("2", Seller.address, ammount, { value: ammount });

    //call releaseEscrow Error
    await expect(
      paydeceEscrow.connect(other).refundBuyerNativeCoin("2")
    ).to.be.revertedWith("Ownable: caller is not the owner");

    //call releaseEscrow
    await paydeceEscrow.connect(owner).refundBuyerNativeCoin("2");

    //get balance sc paydece
    const afterbalanceSC = await ethers.provider.getBalance(
      paydeceEscrow.address
    );
    // console.log("----------afterbalanceSC:"+afterbalanceSC.toString())
    expect(Number(afterbalanceSC)).to.equal(Number(0));

    // sellerBalance = await ethers.provider.getBalance(Seller.address);
    // console.log("sellerBalance:"+sellerBalance.toString())

    // buyerBalance = await ethers.provider.getBalance(Buyer.address);
    // console.log("buyerBalance:"+buyerBalance.toString())

    // 1 is expected because 1% of 100
    // expect(Number(afterbalanceSC)).to.equal(Number(ethers.utils.parseUnits("1", "ether")));

    // expect(Number(buyerBalance)).to.be.at.least(Number(ethers.utils.parseUnits("99", "ether")));
  });

  it("should feesAvailableNativeCoin & withdrawFeesNativeCoin NativeCoin", async function () {
    let PaydeceEscrow, paydeceEscrow;

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    const [owner, Seller, Buyer, other] = await ethers.getSigners();
    // console.log(owner.address)

    const initBuyerBalance = await ethers.provider.getBalance(Seller.address);
    const initSellerBalance = await ethers.provider.getBalance(Buyer.address);

    //Set Fee to 1%
    await paydeceEscrow.connect(owner).setFeeSeller("1000");

    //Error OnlyOwner
    await expect(
      paydeceEscrow.connect(other).withdrawFeesNativeCoin()
    ).to.be.revertedWith("Ownable: caller is not the owner");

    await expect(
      paydeceEscrow.connect(owner).withdrawFeesNativeCoin()
    ).to.be.revertedWith("Amount > feesAvailable");

    //call createEscrow
    const ammount = ethers.utils.parseUnits("100", "ether"); //1 ether
    await paydeceEscrow
      .connect(Buyer)
      .createEscrowNativeCoin("2", Seller.address, ammount, { value: ammount });

    //call releaseEscrow
    await paydeceEscrow.connect(Buyer).releaseEscrowNativeCoin("2");

    //get balance SC paydece
    const afterbalanceSC = await ethers.provider.getBalance(
      paydeceEscrow.address
    );
    // console.log("afterbalanceSC:"+afterbalanceSC.toString())
    // 1 is expected because 1% of 100
    expect(Number(afterbalanceSC)).to.equal(
      Number(ethers.utils.parseUnits("1", "ether"))
    );

    buyerBalance = await ethers.provider.getBalance(Buyer.address);
    // console.log("buyerBalance:"+buyerBalance.toString())

    // console.log("ammount:"+ammount)

    sellerBalance = await ethers.provider.getBalance(Seller.address);
    // console.log("sellerBalance:"+sellerBalance.toString())
    // expect(Number(sellerBalance)).to.equal(Number(initSellerBalance)+Number(ethers.utils.parseUnits("99", "ether")));

    const _feesAvailable = await paydeceEscrow
      .connect(owner)
      .feesAvailableNativeCoin();

    const scBalance = await ethers.provider.getBalance(owner.address);
    //withdrawFees
    const _releaseEscrowOwner = await paydeceEscrow
      .connect(owner)
      .withdrawFeesNativeCoin();

    const txReceipt = await _releaseEscrowOwner.wait();
    const effGasPrice = txReceipt.effectiveGasPrice;
    const txGasUsed = txReceipt.gasUsed;
    const gasUsedETH = effGasPrice * txGasUsed;
    // console.debug(
    //   "Total Gas USD: " + ethers.utils.formatEther(gasUsedETH.toString()) // exchange rate today
    // );

    //get Balance
    const scAfterBalance = await ethers.provider.getBalance(owner.address);
    // console.log("scAfterBalance:" + scAfterBalance);

    expect(Number(scAfterBalance)).to.equal(
      Number(scBalance) + Number(_feesAvailable) - Number(gasUsedETH)
    );
  });
});

describe("Contract Ownership", () => {
  it("you should transfer Ownership", async () => {
    let PaydeceEscrow, paydeceEscrow;
    const [owner, newOwner] = await ethers.getSigners();

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    await paydeceEscrow.transferOwnership(newOwner.address);

    const _owner = await paydeceEscrow.owner();
    // console.log("_owner:" + _owner);
    expect(_owner).to.equal(newOwner.address);
  });

  it("you should renounce Ownership", async () => {
    let PaydeceEscrow, paydeceEscrow;
    //const [owner, newOwner] = await ethers.getSigners();

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    await paydeceEscrow.renounceOwnership();

    const _owner = await paydeceEscrow.owner();
    // console.log("_owner:" + _owner);
    expect(_owner).to.equal("******************************************");
  });

  it("should create a escrow and get state equal 1", async function () {
    let PaydeceEscrow, paydeceEscrow;
    let USDT, usdt;

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    // Deploy USDT
    USDT = await ethers.getContractFactory("USDTToken");
    usdt = await USDT.deploy();
    await usdt.deployed();

    const [owner, Seller, Buyer, other] = await ethers.getSigners();

    //Set Fee to 1%
    await paydeceEscrow.connect(owner).setFeeSeller("1000");

    //call addStablesAddresses Error
    await expect(
      paydeceEscrow.connect(other).addStablesAddresses(usdt.address)
    ).to.be.revertedWith("Ownable: caller is not the owner");

    //call addStablesAddresses
    await paydeceEscrow.connect(owner).addStablesAddresses(usdt.address);

    //Set amount
    const decimals = await usdt.connect(Buyer).decimals();
    const ammount = 100 * 10 ** decimals;
    //  console.log("ammount:"+ammount)

    //transfer
    await usdt.transfer(Buyer.address, ammount);

    //call approve
    await usdt.connect(Buyer).approve(paydeceEscrow.address, ammount);

    //call createEscrow
    await paydeceEscrow
      .connect(Buyer)
      .createEscrow("1", Seller.address, ammount, usdt.address);

    //call getState
    const _state = await paydeceEscrow.getState("1");
    // console.log("_state:" + _state);
    const _FundedState = 1;
    expect(_state).to.equal(_FundedState);
  });

  it("should create a escrow and get value equal 1", async function () {
    let PaydeceEscrow, paydeceEscrow;
    let USDT, usdt;

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    // Deploy USDT
    USDT = await ethers.getContractFactory("USDTToken");
    usdt = await USDT.deploy();
    await usdt.deployed();

    const [owner, Seller, Buyer, other] = await ethers.getSigners();

    //Set Fee to 1%
    await paydeceEscrow.connect(owner).setFeeSeller("1000");

    //call addStablesAddresses
    await paydeceEscrow.connect(owner).addStablesAddresses(usdt.address);

    //Set amount
    const decimals = await usdt.connect(Buyer).decimals();
    const ammount = 100 * 10 ** decimals;
    //  console.log("ammount:"+ammount)

    //transfer
    await usdt.transfer(Buyer.address, ammount);

    //call approve
    await usdt.connect(Buyer).approve(paydeceEscrow.address, ammount);

    //call createEscrow
    await paydeceEscrow
      .connect(Buyer)
      .createEscrow("1", Seller.address, ammount, usdt.address);

    //call getState
    const _value = await paydeceEscrow.getValue("1");
    //console.table(_state);
    // const _FundedState = 1;
    expect(_value).to.equal(ammount);
  });

  it("should create a escrow and get Escrow", async function () {
    let PaydeceEscrow, paydeceEscrow;
    let USDT, usdt;

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    // Deploy USDT
    USDT = await ethers.getContractFactory("USDTToken");
    usdt = await USDT.deploy();
    await usdt.deployed();

    const [owner, Seller, Buyer, other] = await ethers.getSigners();

    //Set Fee to 1%
    await paydeceEscrow.connect(owner).setFeeSeller("1000");

    //call addStablesAddresses
    await paydeceEscrow.connect(owner).addStablesAddresses(usdt.address);

    //Set amount
    const decimals = await usdt.connect(Buyer).decimals();
    const ammount = 100 * 10 ** decimals;
    //  console.log("ammount:"+ammount)

    //transfer
    await usdt.transfer(Buyer.address, ammount);

    //call approve
    await usdt.connect(Buyer).approve(paydeceEscrow.address, ammount);

    //call createEscrow
    await paydeceEscrow
      .connect(Buyer)
      .createEscrow("1", Seller.address, ammount, usdt.address);

    //call getState
    const _escrow = await paydeceEscrow.escrows("1");
    // console.table(_escrow);
    // console.debug("_escrow.Buyer:" + _escrow.buyer);
    // const _FundedState = 1;
    expect(_escrow.buyer).to.equal(Buyer.address);
    expect(_escrow.seller).to.equal(Seller.address);
    expect(_escrow.value).to.equal(ammount);
    expect(_escrow.sellerfee).to.equal(1000);
    expect(_escrow.buyerfee).to.equal(0);
    expect(_escrow.currency).to.equal(usdt.address);
    expect(_escrow.status).to.equal(1);
  });
});

describe("Stuck Buyer Fees Bug", () => {
  it("should demonstrate buyer fees stuck after refund", async () => {
    let PaydeceEscrow, paydeceEscrow, USDT, usdt;
    const [owner, Seller, Buyer] = await ethers.getSigners();

    // Deploy contracts
    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    USDT = await ethers.getContractFactory("USDTToken");
    usdt = await USDT.deploy();
    await usdt.deployed();

    // Setup: 1% buyer fee, whitelist token
    await paydeceEscrow.setFeeBuyer(10); // 1% = 10/1000
    await paydeceEscrow.addStablesAddresses(usdt.address);

    const decimals = await usdt.decimals();
    const amount = 100 * 10 ** decimals;
    // Fee calculation: (value * (feeBuyer * 10^decimals)) / (100 * 10^decimals) / 1000
    // With feeBuyer=10, value=100*10^6, decimals=6: (100*10^6 * 10*10^6) / (100*10^6) / 1000 = 10*10^6 / 1000 = 10^4
    const buyerFee = (amount * 10 * (10 ** decimals)) / (100 * (10 ** decimals)) / 1000;

    // Fund buyer and approve
    await usdt.transfer(Buyer.address, amount + buyerFee);
    await usdt.connect(Buyer).approve(paydeceEscrow.address, amount + buyerFee);

    // Create escrow - buyer pays amount + buyerFee
    await paydeceEscrow.connect(Buyer).createEscrow("1", Seller.address, amount, usdt.address);

    // Verify contract received amount + buyerFee
    const contractBalanceAfterDeposit = await usdt.balanceOf(paydeceEscrow.address);
    expect(contractBalanceAfterDeposit).to.equal(amount + buyerFee);

    // Verify no fees recorded yet
    const feesBeforeRefund = await paydeceEscrow.feesAvailable(usdt.address);
    expect(feesBeforeRefund).to.equal(0);

    // Refund - only returns amount, not buyerFee
    await paydeceEscrow.refundBuyer("1");

    // Verify buyer only got amount back (not buyerFee)
    const buyerBalanceAfterRefund = await usdt.balanceOf(Buyer.address);
    expect(buyerBalanceAfterRefund).to.equal(amount); // Missing buyerFee

    // Verify contract still holds buyerFee
    const contractBalanceAfterRefund = await usdt.balanceOf(paydeceEscrow.address);
    expect(contractBalanceAfterRefund).to.equal(buyerFee);

    // Verify fees still not recorded (so owner can't withdraw)
    const feesAfterRefund = await paydeceEscrow.feesAvailable(usdt.address);
    expect(feesAfterRefund).to.equal(0);

    // Prove owner cannot withdraw the stuck buyerFee
    await expect(paydeceEscrow.withdrawFees(usdt.address))
      .to.be.revertedWith("Amount > feesAvailable");

    // The buyerFee is permanently stuck in the contract
  });

  it("should demonstrate buyer fees stuck after native coin refund", async () => {
    let PaydeceEscrow, paydeceEscrow;
    const [owner, Seller, Buyer] = await ethers.getSigners();

    // Deploy contract
    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    // Setup: 1% buyer fee
    await paydeceEscrow.setFeeBuyer(10); // 1% = 10/1000

    const amount = ethers.utils.parseUnits("100", "ether");
    // Fee calculation for native coin: (value * (feeBuyer * 10^18)) / (100 * 10^18) / 1000
    const buyerFee = amount.mul(10).div(1000); // 1% of 100 ETH = 1 ETH

    // Create escrow - buyer pays amount + buyerFee
    await paydeceEscrow.connect(Buyer).createEscrowNativeCoin("1", Seller.address, amount, {
      value: amount.add(buyerFee)
    });

    // Verify contract received amount + buyerFee
    const contractBalanceAfterDeposit = await ethers.provider.getBalance(paydeceEscrow.address);
    expect(contractBalanceAfterDeposit).to.equal(amount.add(buyerFee));

    // Verify no fees recorded yet
    const feesBeforeRefund = await paydeceEscrow.feesAvailableNativeCoin();
    expect(feesBeforeRefund).to.equal(0);

    // Refund - only returns amount, not buyerFee
    await paydeceEscrow.refundBuyerNativeCoin("1");

    // Verify contract still holds buyerFee
    const contractBalanceAfterRefund = await ethers.provider.getBalance(paydeceEscrow.address);
    expect(contractBalanceAfterRefund).to.equal(buyerFee);

    // Verify fees still not recorded (so owner can't withdraw)
    const feesAfterRefund = await paydeceEscrow.feesAvailableNativeCoin();
    expect(feesAfterRefund).to.equal(0);

    // Prove owner cannot withdraw the stuck buyerFee
    await expect(paydeceEscrow.withdrawFeesNativeCoin())
      .to.be.revertedWith("Amount > feesAvailable");

    // The buyerFee is permanently stuck in the contract
  });
});

describe("Contract Read Methods", () => {
  it("version should be 3.0.0", async () => {
    let PaydeceEscrow, paydeceEscrow;
    const [owner, newOwner] = await ethers.getSigners();

    PaydeceEscrow = await ethers.getContractFactory("PaydeceEscrow");
    paydeceEscrow = await PaydeceEscrow.deploy();
    await paydeceEscrow.deployed();

    const _verion = await paydeceEscrow.version();

    //console.log("_verion:" + _verion);
    expect(_verion).to.equal("3.0.0");
  });
});

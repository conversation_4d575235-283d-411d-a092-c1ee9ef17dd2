
async function main () {
    const address = '******************************************';
  
    const accounts = await ethers.getSigners()
    
    const escrow = await ethers.getContractAt("CriptoCarsEscrow",address,accounts[1])
  
     await escrow.releaseEscrow(1);
    
    console.log("Termino");
    // var balance = await usdt.balanceOf('******************************************');
    // console.log(balance.toString());
  }
  
  main()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
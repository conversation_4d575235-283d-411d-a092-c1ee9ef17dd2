async function main () {
    const address = '******************************************'; //Address Escrow
  
    const accounts = await ethers.getSigners()
    
    const escrow = await ethers.getContractAt("CriptoCarsEscrow",address,accounts[1])
  
    console.log( await escrow.version());

    //console.log('Transferring ...');
    // createEscrow(uint _orderId, address payable _buyer, address payable _seller, uint _value, IERC20 _currency)

     await escrow.createEscrow(1, '******************************************','******************************************', 
                                    100000000000000000000,'******************************************');
    

    console.log("Termino");
    // var balance = await usdt.balanceOf('******************************************');
    // console.log(balance.toString());
  }
  
  main()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
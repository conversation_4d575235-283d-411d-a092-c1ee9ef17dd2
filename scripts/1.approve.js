
async function main () {
    const address = '******************************************';
  
    const accounts = await ethers.getSigners()
    console.log(await accounts[1].address)
  
    //const usdt = await ethers.getContractFactory("USDTToken",address);

    //const usdt = await ethers.getContractAt("USDTToken",address)

    // const MyContract = await ethers.getContractFactory("USDTToken");
    // const usdt = new ethers.Contract(MyContract, MyContract.interface, accounts[1]);

    const usdt = await ethers.getContractAt("USDTToken",address,accounts[1])


////const usdt = await ethers.Contract(MyContract, MyContract.interface, accounts[0]);
  
    console.log(await usdt.decimals());
  
    //await usdt.transfer('******************************************', '1', { from: accounts[1].address});
    await usdt.approve('******************************************',105);
  
    console.log("Termino");
    // var balance = await usdt.balanceOf('******************************************');
    // console.log(balance.toString());
  }
  
  main()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
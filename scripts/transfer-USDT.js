
async function main () {
  // const address = '******************************************';
  const address = '******************************************';

  const accounts = await ethers.getSigners()

  // ******************************************
  // const wallet = new Wallet( '******************************************');

  console.log(await accounts[0].address)

  const usdt = await ethers.getContractAt("USDTToken",address,accounts[0])
  //console.log(await usdt.decimals())
  
  // console.log('Transferring ...');

  await usdt.transfer('******************************************','105000000000000000000') ;

  var balance = await usdt.balanceOf('******************************************');
  console.log(balance.toString());
}

main()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  });
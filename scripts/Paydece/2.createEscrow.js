async function main () {
    const address = '******************************************'; //Address Escrow
  
    const accounts = await ethers.getSigners()
    
    const escrow = await ethers.getContractAt("PaydeceEscrow",address,accounts[0])
  
    console.log( await escrow.version());

    //console.log('Transferring ...');
    // createEscrow(uint _orderId, address payable _buyer, address payable _seller, uint _value, IERC20 _currency)

     await escrow.createEscrow(2, '******************************************','******************************************', '1','******************************************');
    

    console.log("Termino");
    // var balance = await usdt.balanceOf('******************************************');
    // console.log(balance.toString());
  }
  
  main()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
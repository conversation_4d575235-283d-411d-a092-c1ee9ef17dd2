
async function main () {
    const address = '******************************************'; //Stable
  
    const accounts = await ethers.getSigners()
    console.log(await accounts[0].address)
  
    const usdt = await ethers.getContractAt("USDTToken",address,accounts[0])

    console.log(await usdt.decimals());
  
    await usdt.approve('******************************************','1000000');


    // console.log(await usdt.allowance( accounts[1], '******************************************'));
  
    console.log("Termino");
    // var balance = await usdt.balanceOf('******************************************');
    // console.log(balance.toString());
  }
  
  main()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
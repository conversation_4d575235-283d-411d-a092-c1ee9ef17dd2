async function main () {
    const address = '******************************************'; //Address Escrow
  
    const accounts = await ethers.getSigners()
    
    const escrow = await ethers.getContractAt("PaydeceEscrow",address,accounts[0])
  
    console.log( await escrow.version());

    console.log("Procesando releaseEscrow...");
    await escrow.releaseEscrow(1);    
    console.log("Termino");
    // var balance = await usdt.balanceOf('******************************************');
    // console.log(balance.toString());
  }
  
  main()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
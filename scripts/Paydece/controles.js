
async function main () {
    const address = '******************************************';
  
    const accounts = await ethers.getSigners()
    
    console.log(await accounts[0].address)
  
    const usdt = await ethers.getContractAt("USDTToken",address,accounts[0])
  
    //console.log('Transferring ...');  
    //await usdt.transfer('******************************************','105000000000000000000') ;
  
    var balance = await usdt.balanceOf('******************************************');
    console.log('Balances ',balance.toString());

    
  }
  
  main()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
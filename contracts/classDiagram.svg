<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 5.0.0 (20220707.1540)
 -->
<!-- Title: UmlClassDiagram Pages: 1 -->
<svg width="1529pt" height="1273pt"
 viewBox="0.00 0.00 1529.25 1272.60" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1268.6)">
<title>UmlClassDiagram</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-1268.6 1525.25,-1268.6 1525.25,4 -4,4"/>
<!-- 0 -->
<g id="node1" class="node">
<title>0</title>
<polygon fill="#f2f2f2" stroke="black" points="159.48,-1012.9 159.48,-1264.1 598.1,-1264.1 598.1,-1012.9 159.48,-1012.9"/>
<text text-anchor="middle" x="378.79" y="-1247.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Interface&gt;&gt;</text>
<text text-anchor="middle" x="378.79" y="-1230.7" font-family="Times,serif" font-size="14.00">IERC20</text>
<text text-anchor="middle" x="378.79" y="-1213.9" font-family="Times,serif" font-size="14.00">PaydeceEscrowV3.sol</text>
<polyline fill="none" stroke="black" points="159.48,-1205.7 598.1,-1205.7 "/>
<text text-anchor="start" x="167.48" y="-1189.1" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="167.48" y="-1172.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;decimals(): uint8</text>
<text text-anchor="start" x="167.48" y="-1155.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;totalSupply(): uint256</text>
<text text-anchor="start" x="167.48" y="-1138.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;balanceOf(account: address): uint256</text>
<text text-anchor="start" x="167.48" y="-1121.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transfer(recipient: address, amount: uint256): bool</text>
<text text-anchor="start" x="167.48" y="-1105.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;allowance(owner: address, spender: address): uint256</text>
<text text-anchor="start" x="167.48" y="-1088.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;approve(spender: address, amount: uint256): bool</text>
<text text-anchor="start" x="167.48" y="-1071.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&#160;transferFrom(sender: address, recipient: address, amount: uint256): bool</text>
<text text-anchor="start" x="167.48" y="-1054.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="167.48" y="-1037.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Transfer(from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="167.48" y="-1021.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Approval(owner: address, spender: address, value: uint256)</text>
</g>
<!-- 1 -->
<g id="node2" class="node">
<title>1</title>
<polygon fill="#f2f2f2" stroke="black" points="615.72,-1021.3 615.72,-1255.7 1181.85,-1255.7 1181.85,-1021.3 615.72,-1021.3"/>
<text text-anchor="middle" x="898.79" y="-1239.1" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="898.79" y="-1222.3" font-family="Times,serif" font-size="14.00">Address</text>
<text text-anchor="middle" x="898.79" y="-1205.5" font-family="Times,serif" font-size="14.00">PaydeceEscrowV3.sol</text>
<polyline fill="none" stroke="black" points="615.72,-1197.3 1181.85,-1197.3 "/>
<text text-anchor="start" x="623.72" y="-1180.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="623.72" y="-1163.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;isContract(account: address): bool</text>
<text text-anchor="start" x="623.72" y="-1147.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;sendValue(recipient: address, amount: uint256)</text>
<text text-anchor="start" x="623.72" y="-1130.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="623.72" y="-1113.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCall(target: address, data: bytes, errorMessage: string): bytes</text>
<text text-anchor="start" x="623.72" y="-1096.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCallWithValue(target: address, data: bytes, value: uint256): bytes</text>
<text text-anchor="start" x="623.72" y="-1079.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionCallWithValue(target: address, data: bytes, value: uint256, errorMessage: string): bytes</text>
<text text-anchor="start" x="623.72" y="-1063.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionStaticCall(target: address, data: bytes): bytes</text>
<text text-anchor="start" x="623.72" y="-1046.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;functionStaticCall(target: address, data: bytes, errorMessage: string): bytes</text>
<text text-anchor="start" x="623.72" y="-1029.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;verifyCallResult(success: bool, returndata: bytes, errorMessage: string): bytes</text>
</g>
<!-- 2 -->
<g id="node3" class="node">
<title>2</title>
<polygon fill="#f2f2f2" stroke="black" points="340.62,-737.5 340.62,-938.3 802.96,-938.3 802.96,-737.5 340.62,-737.5"/>
<text text-anchor="middle" x="571.79" y="-921.7" font-family="Times,serif" font-size="14.00">&lt;&lt;Library&gt;&gt;</text>
<text text-anchor="middle" x="571.79" y="-904.9" font-family="Times,serif" font-size="14.00">SafeERC20</text>
<text text-anchor="middle" x="571.79" y="-888.1" font-family="Times,serif" font-size="14.00">PaydeceEscrowV3.sol</text>
<polyline fill="none" stroke="black" points="340.62,-879.9 802.96,-879.9 "/>
<text text-anchor="start" x="348.62" y="-863.3" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="348.62" y="-846.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_callOptionalReturn(token: IERC20, data: bytes)</text>
<text text-anchor="start" x="348.62" y="-829.7" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="348.62" y="-812.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeTransfer(token: IERC20, to: address, value: uint256)</text>
<text text-anchor="start" x="348.62" y="-796.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeTransferFrom(token: IERC20, from: address, to: address, value: uint256)</text>
<text text-anchor="start" x="348.62" y="-779.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeApprove(token: IERC20, spender: address, value: uint256)</text>
<text text-anchor="start" x="348.62" y="-762.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeIncreaseAllowance(token: IERC20, spender: address, value: uint256)</text>
<text text-anchor="start" x="348.62" y="-745.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;safeDecreaseAllowance(token: IERC20, spender: address, value: uint256)</text>
</g>
<!-- 2&#45;&gt;0 -->
<g id="edge2" class="edge">
<title>2&#45;&gt;0</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M507.52,-938.33C493.86,-959.47 479.23,-982.11 464.9,-1004.27"/>
<polygon fill="black" stroke="black" points="461.92,-1002.44 459.43,-1012.74 467.79,-1006.24 461.92,-1002.44"/>
</g>
<!-- 2&#45;&gt;1 -->
<g id="edge1" class="edge">
<title>2&#45;&gt;1</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M680.67,-938.33C707.37,-962.7 736.25,-989.08 764,-1014.41"/>
<polygon fill="black" stroke="black" points="761.66,-1017.02 771.4,-1021.18 766.38,-1011.85 761.66,-1017.02"/>
</g>
<!-- 3 -->
<g id="node4" class="node">
<title>3</title>
<polygon fill="#f2f2f2" stroke="black" points="820.77,-716.7 820.77,-959.1 1018.81,-959.1 1018.81,-716.7 820.77,-716.7"/>
<text text-anchor="middle" x="919.79" y="-942.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="919.79" y="-925.7" font-family="Times,serif" font-size="14.00">ReentrancyGuard</text>
<text text-anchor="middle" x="919.79" y="-908.9" font-family="Times,serif" font-size="14.00">PaydeceEscrowV3.sol</text>
<polyline fill="none" stroke="black" points="820.77,-900.7 1018.81,-900.7 "/>
<text text-anchor="start" x="828.77" y="-884.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="828.77" y="-867.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;_NOT_ENTERED: uint256</text>
<text text-anchor="start" x="828.77" y="-850.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;_ENTERED: uint256</text>
<text text-anchor="start" x="828.77" y="-833.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;_status: uint256</text>
<polyline fill="none" stroke="black" points="820.77,-825.5 1018.81,-825.5 "/>
<text text-anchor="start" x="828.77" y="-808.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="828.77" y="-792.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_nonReentrantBefore()</text>
<text text-anchor="start" x="828.77" y="-775.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_nonReentrantAfter()</text>
<text text-anchor="start" x="828.77" y="-758.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="828.77" y="-741.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; nonReentrant()</text>
<text text-anchor="start" x="828.77" y="-724.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor()</text>
</g>
<!-- 4 -->
<g id="node5" class="node">
<title>4</title>
<polygon fill="#f2f2f2" stroke="black" points="1199.83,-1080.1 1199.83,-1196.9 1357.75,-1196.9 1357.75,-1080.1 1199.83,-1080.1"/>
<text text-anchor="middle" x="1278.79" y="-1180.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1278.79" y="-1163.5" font-family="Times,serif" font-size="14.00">Context</text>
<text text-anchor="middle" x="1278.79" y="-1146.7" font-family="Times,serif" font-size="14.00">PaydeceEscrowV3.sol</text>
<polyline fill="none" stroke="black" points="1199.83,-1138.5 1357.75,-1138.5 "/>
<text text-anchor="start" x="1207.83" y="-1121.9" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1207.83" y="-1105.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgSender(): address</text>
<text text-anchor="start" x="1207.83" y="-1088.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_msgData(): bytes</text>
</g>
<!-- 5 -->
<g id="node6" class="node">
<title>5</title>
<polygon fill="#f2f2f2" stroke="black" points="1036.32,-699.9 1036.32,-975.9 1521.25,-975.9 1521.25,-699.9 1036.32,-699.9"/>
<text text-anchor="middle" x="1278.79" y="-959.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Abstract&gt;&gt;</text>
<text text-anchor="middle" x="1278.79" y="-942.5" font-family="Times,serif" font-size="14.00">Ownable</text>
<text text-anchor="middle" x="1278.79" y="-925.7" font-family="Times,serif" font-size="14.00">PaydeceEscrowV3.sol</text>
<polyline fill="none" stroke="black" points="1036.32,-917.5 1521.25,-917.5 "/>
<text text-anchor="start" x="1044.32" y="-900.9" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="1044.32" y="-884.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;_owner: address</text>
<polyline fill="none" stroke="black" points="1036.32,-875.9 1521.25,-875.9 "/>
<text text-anchor="start" x="1044.32" y="-859.3" font-family="Times,serif" font-size="14.00">Internal:</text>
<text text-anchor="start" x="1044.32" y="-842.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_checkOwner()</text>
<text text-anchor="start" x="1044.32" y="-825.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_transferOwnership(newOwner: address)</text>
<text text-anchor="start" x="1044.32" y="-808.9" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="1044.32" y="-792.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; OwnershipTransferred(previousOwner: address, newOwner: address)</text>
<text text-anchor="start" x="1044.32" y="-775.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyOwner()</text>
<text text-anchor="start" x="1044.32" y="-758.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor()</text>
<text text-anchor="start" x="1044.32" y="-741.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;owner(): address</text>
<text text-anchor="start" x="1044.32" y="-724.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;renounceOwnership() &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="1044.32" y="-708.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;transferOwnership(newOwner: address) &lt;&lt;onlyOwner&gt;&gt;</text>
</g>
<!-- 5&#45;&gt;4 -->
<g id="edge3" class="edge">
<title>5&#45;&gt;4</title>
<path fill="none" stroke="black" d="M1278.79,-975.96C1278.79,-1001.01 1278.79,-1026.52 1278.79,-1049.65"/>
<polygon fill="none" stroke="black" points="1268.29,-1049.95 1278.79,-1079.95 1289.29,-1049.95 1268.29,-1049.95"/>
</g>
<!-- 7 -->
<g id="node7" class="node">
<title>7</title>
<polygon fill="#f2f2f2" stroke="black" points="0,-1054.9 0,-1222.1 141.58,-1222.1 141.58,-1054.9 0,-1054.9"/>
<text text-anchor="middle" x="70.79" y="-1205.5" font-family="Times,serif" font-size="14.00">&lt;&lt;Enum&gt;&gt;</text>
<text text-anchor="middle" x="70.79" y="-1188.7" font-family="Times,serif" font-size="14.00">EscrowStatus</text>
<text text-anchor="middle" x="70.79" y="-1171.9" font-family="Times,serif" font-size="14.00">PaydeceEscrowV3.sol</text>
<polyline fill="none" stroke="black" points="0,-1163.7 141.58,-1163.7 "/>
<text text-anchor="start" x="8" y="-1147.1" font-family="Times,serif" font-size="14.00">Unknown: 0</text>
<text text-anchor="start" x="8" y="-1130.3" font-family="Times,serif" font-size="14.00">Funded: 1</text>
<text text-anchor="start" x="8" y="-1113.5" font-family="Times,serif" font-size="14.00">NOT_USED: 2</text>
<text text-anchor="start" x="8" y="-1096.7" font-family="Times,serif" font-size="14.00">Completed: 3</text>
<text text-anchor="start" x="8" y="-1079.9" font-family="Times,serif" font-size="14.00">Refund: 4</text>
<text text-anchor="start" x="8" y="-1063.1" font-family="Times,serif" font-size="14.00">Arbitration: 5</text>
</g>
<!-- 6 -->
<g id="node9" class="node">
<title>6</title>
<polygon fill="#f2f2f2" stroke="black" points="180.08,-0.5 180.08,-662.9 703.5,-662.9 703.5,-0.5 180.08,-0.5"/>
<text text-anchor="middle" x="441.79" y="-646.3" font-family="Times,serif" font-size="14.00">PaydeceEscrow</text>
<text text-anchor="middle" x="441.79" y="-629.5" font-family="Times,serif" font-size="14.00">PaydeceEscrowV3.sol</text>
<polyline fill="none" stroke="black" points="180.08,-621.3 703.5,-621.3 "/>
<text text-anchor="start" x="188.08" y="-604.7" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="188.08" y="-587.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;feeSeller: uint256</text>
<text text-anchor="start" x="188.08" y="-571.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;feeBuyer: uint256</text>
<text text-anchor="start" x="188.08" y="-554.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;escrows: mapping(uint=&gt;Escrow)</text>
<text text-anchor="start" x="188.08" y="-537.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;whitelistedStablesAddresses: mapping(address=&gt;bool)</text>
<text text-anchor="start" x="188.08" y="-520.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;feesAvailable: mapping(IERC20=&gt;uint)</text>
<text text-anchor="start" x="188.08" y="-503.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;feesAvailableNativeCoin: uint256</text>
<polyline fill="none" stroke="black" points="180.08,-495.7 703.5,-495.7 "/>
<text text-anchor="start" x="188.08" y="-479.1" font-family="Times,serif" font-size="14.00">Private:</text>
<text text-anchor="start" x="188.08" y="-462.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_releaseEscrow(_orderId: uint) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-445.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;_releaseEscrowNativeCoin(_orderId: uint) &lt;&lt;nonReentrant&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-428.7" font-family="Times,serif" font-size="14.00">External:</text>
<text text-anchor="start" x="188.08" y="-411.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;payable&gt;&gt; createEscrowNativeCoin(_orderId: uint, _seller: address, _value: uint256)</text>
<text text-anchor="start" x="188.08" y="-395.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setFeeSeller(_feeSeller: uint256) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-378.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;setFeeBuyer(_feeBuyer: uint256) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-361.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;createEscrow(_orderId: uint, _seller: address, _value: uint256, _currency: IERC20)</text>
<text text-anchor="start" x="188.08" y="-344.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;releaseEscrowOwner(_orderId: uint) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-327.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;releaseEscrowOwnerNativeCoin(_orderId: uint) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-311.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;releaseEscrow(_orderId: uint) &lt;&lt;onlyBuyer&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-294.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;releaseEscrowNativeCoin(_orderId: uint) &lt;&lt;onlyBuyer&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-277.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;refundBuyer(_orderId: uint) &lt;&lt;nonReentrant, onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-260.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;refundBuyerNativeCoin(_orderId: uint) &lt;&lt;nonReentrant, onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-243.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;withdrawFees(_currency: IERC20) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-227.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;withdrawFeesNativeCoin() &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-210.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;version(): string</text>
<text text-anchor="start" x="188.08" y="-193.5" font-family="Times,serif" font-size="14.00">Public:</text>
<text text-anchor="start" x="188.08" y="-176.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; EscrowDeposit(orderId: uint, escrow: Escrow)</text>
<text text-anchor="start" x="188.08" y="-159.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; EscrowComplete(orderId: uint, escrow: Escrow)</text>
<text text-anchor="start" x="188.08" y="-143.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; EscrowDisputeResolved(orderId: uint)</text>
<text text-anchor="start" x="188.08" y="-126.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;event&gt;&gt; Log(step: string, data: uint256)</text>
<text text-anchor="start" x="188.08" y="-109.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlyBuyer(_orderId: uint)</text>
<text text-anchor="start" x="188.08" y="-92.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;&lt;&lt;modifier&gt;&gt; onlySeller(_orderId: uint)</text>
<text text-anchor="start" x="188.08" y="-75.9" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;constructor()</text>
<text text-anchor="start" x="188.08" y="-59.1" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getState(_orderId: uint): EscrowStatus</text>
<text text-anchor="start" x="188.08" y="-42.3" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;getValue(_orderId: uint): uint256</text>
<text text-anchor="start" x="188.08" y="-25.5" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;addStablesAddresses(_addressStableToWhitelist: address) &lt;&lt;onlyOwner&gt;&gt;</text>
<text text-anchor="start" x="188.08" y="-8.7" font-family="Times,serif" font-size="14.00"> &#160;&#160;&#160;delStablesAddresses(_addressStableToWhitelist: address) &lt;&lt;onlyOwner&gt;&gt;</text>
</g>
<!-- 7&#45;&gt;6 -->
<g id="edge6" class="edge">
<title>7&#45;&gt;6</title>
<path fill="none" stroke="black" d="M75.97,-1054.65C79.8,-963.67 91.01,-815.13 142.79,-699.4 151.78,-679.3 162.21,-659.43 173.68,-639.96"/>
<polygon fill="black" stroke="black" points="173.78,-639.79 173.44,-632.59 179.96,-629.5 180.3,-636.71 173.78,-639.79"/>
</g>
<!-- 8 -->
<g id="node8" class="node">
<title>8</title>
<polygon fill="#f2f2f2" stroke="black" points="143,-745.9 143,-929.9 284.58,-929.9 284.58,-745.9 143,-745.9"/>
<text text-anchor="middle" x="213.79" y="-913.3" font-family="Times,serif" font-size="14.00">&lt;&lt;Struct&gt;&gt;</text>
<text text-anchor="middle" x="213.79" y="-896.5" font-family="Times,serif" font-size="14.00">Escrow</text>
<text text-anchor="middle" x="213.79" y="-879.7" font-family="Times,serif" font-size="14.00">PaydeceEscrowV3.sol</text>
<polyline fill="none" stroke="black" points="143,-871.5 284.58,-871.5 "/>
<text text-anchor="start" x="151" y="-854.9" font-family="Times,serif" font-size="14.00">buyer: address</text>
<text text-anchor="start" x="151" y="-838.1" font-family="Times,serif" font-size="14.00">seller: address</text>
<text text-anchor="start" x="151" y="-821.3" font-family="Times,serif" font-size="14.00">value: uint256</text>
<text text-anchor="start" x="151" y="-804.5" font-family="Times,serif" font-size="14.00">sellerfee: uint256</text>
<text text-anchor="start" x="151" y="-787.7" font-family="Times,serif" font-size="14.00">buyerfee: uint256</text>
<text text-anchor="start" x="151" y="-770.9" font-family="Times,serif" font-size="14.00">currency: IERC20</text>
<text text-anchor="start" x="151" y="-754.1" font-family="Times,serif" font-size="14.00">status: EscrowStatus</text>
</g>
<!-- 8&#45;&gt;0 -->
<g id="edge4" class="edge">
<title>8&#45;&gt;0</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M264.19,-930.11C277.08,-953.43 291.2,-978.99 305,-1003.96"/>
<polygon fill="black" stroke="black" points="301.96,-1005.7 309.86,-1012.76 308.09,-1002.31 301.96,-1005.7"/>
</g>
<!-- 8&#45;&gt;7 -->
<g id="edge5" class="edge">
<title>8&#45;&gt;7</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M170.11,-930.11C152.58,-966.71 132.41,-1008.84 114.84,-1045.52"/>
<polygon fill="black" stroke="black" points="111.6,-1044.19 110.43,-1054.72 117.91,-1047.21 111.6,-1044.19"/>
</g>
<!-- 8&#45;&gt;6 -->
<g id="edge7" class="edge">
<title>8&#45;&gt;6</title>
<path fill="none" stroke="black" d="M260.44,-745.54C270.63,-723.99 281.93,-699.76 293.76,-674.11"/>
<polygon fill="black" stroke="black" points="293.84,-673.93 292.72,-666.81 298.86,-663.03 299.99,-670.15 293.84,-673.93"/>
</g>
<!-- 6&#45;&gt;0 -->
<g id="edge12" class="edge">
<title>6&#45;&gt;0</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M337.94,-663.02C335.62,-675.24 333.55,-687.4 331.79,-699.4 313.91,-821.21 314.37,-854.53 331.79,-976.4 333.02,-984.99 334.59,-993.77 336.42,-1002.56"/>
<polygon fill="black" stroke="black" points="333.04,-1003.47 338.59,-1012.5 339.88,-1001.98 333.04,-1003.47"/>
</g>
<!-- 6&#45;&gt;2 -->
<g id="edge11" class="edge">
<title>6&#45;&gt;2</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M526.96,-663.03C532.78,-685.62 538.37,-707.27 543.5,-727.17"/>
<polygon fill="black" stroke="black" points="540.17,-728.29 546.06,-737.1 546.95,-726.54 540.17,-728.29"/>
</g>
<!-- 6&#45;&gt;3 -->
<g id="edge8" class="edge">
<title>6&#45;&gt;3</title>
<path fill="none" stroke="black" d="M703.74,-584.19C735.57,-616.75 767.38,-650.26 806.74,-694.14"/>
<polygon fill="none" stroke="black" points="798.91,-701.14 826.7,-716.56 814.6,-687.17 798.91,-701.14"/>
</g>
<!-- 6&#45;&gt;5 -->
<g id="edge9" class="edge">
<title>6&#45;&gt;5</title>
<path fill="none" stroke="black" d="M703.75,-490.5C806.31,-552.29 923.53,-622.9 1025.27,-684.18"/>
<polygon fill="none" stroke="black" points="1020,-693.27 1051.12,-699.76 1030.84,-675.28 1020,-693.27"/>
</g>
<!-- 6&#45;&gt;7 -->
<g id="edge13" class="edge">
<title>6&#45;&gt;7</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M179.92,-601.66C158.74,-633.08 139.77,-665.91 124.79,-699.4 74.88,-810.95 62.67,-952.98 62.73,-1044.57"/>
<polygon fill="black" stroke="black" points="59.23,-1044.67 62.79,-1054.65 66.23,-1044.63 59.23,-1044.67"/>
</g>
<!-- 6&#45;&gt;8 -->
<g id="edge10" class="edge">
<title>6&#45;&gt;8</title>
<path fill="none" stroke="black" d="M285.96,-663.03C274.39,-689.06 263.49,-713.85 253.83,-736.14"/>
<polygon fill="black" stroke="black" points="250.52,-734.97 249.76,-745.54 256.94,-737.75 250.52,-734.97"/>
</g>
</g>
</svg>

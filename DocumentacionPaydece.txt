Paso para deploy

0) npx hardhat compile
1) npx hardhat run --network bsctestnet .\scripts\Paydece\deploy.js
2) npx hardhat verify --contract "contracts/PaydeceEscrow.sol:PaydeceEscrow"  --network polygontest 0x84E49a18956a96f3d3Ff8A923A783C39a8c01774 "0x605F3453C158ad76238f88152430ADC4328b07B4"
3) npx hardhat verify --contract "contracts/USDTToken.sol:USDTToken"  --network polygon 0x605F3453C158ad76238f88152430ADC4328b07B4

npx hardhat verify --contract "contracts/PaydeceEscrowV3sol:PaydeceEscrow"  --network bsctestnet 0x96Ea9780ed771E21e171d2f7aBC5206041EBb467

npx hardhat verify --contract "contracts/USDTToken.sol:USDTToken"  --network bsctestnet 0x96Ea9780ed771E21e171d2f7aBC5206041EBb467

Pragma statements
Import statements
Interfaces
Libraries
Contracts

State variables
Events
Function Modifiers
Struct, <PERSON>rra<PERSON> or Enums
Constructor
Fallback — Receive function
External visible functions
Public visible functions
Internal visible functions
Private visible functions